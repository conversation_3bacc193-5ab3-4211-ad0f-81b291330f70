/* Custom Search Box Component */

.custom-search-box {
  width: 100%;
  max-width: 280px;
  min-width: 170px;
}

.custom-search-box__container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid rgba(232, 234, 237, 0.3);
  border-radius: 50px;
  padding: 0;
  transition: all 0.2s cubic-bezier(0.2, 0, 0, 1);
  overflow: hidden;
}

.custom-search-box__container:hover {
  border-color: rgba(232, 234, 237, 0.5);
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
}

.custom-search-box__container:focus-within {
  border-color: #ccb6ff;
  box-shadow: 0 0 0 2px rgba(204, 182, 255, 0.2);
}

.custom-search-box__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  color: rgba(232, 234, 237, 0.7);
  flex-shrink: 0;
  transition: color 0.2s ease;
}

.custom-search-box__container:focus-within .custom-search-box__icon {
  color: #ccb6ff;
}

.custom-search-box__input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  padding: 17px 8px 17px 0;
  font-size: 0.875rem;
  font-family: inherit;
  color: #e8eaed;
  min-width: 0;
}

.custom-search-box__input::placeholder {
  color: rgba(232, 234, 237, 0.6);
  font-weight: 400;
}

.custom-search-box__input:focus::placeholder {
  color: rgba(232, 234, 237, 0.4);
}

.custom-search-box__clear {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  margin-right: 4px;
  background: none;
  border: none;
  border-radius: 50%;
  color: rgba(232, 234, 237, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.custom-search-box__clear:hover {
  background-color: rgba(232, 234, 237, 0.1);
  color: rgba(232, 234, 237, 0.8);
}

.custom-search-box__clear:active {
  background-color: rgba(232, 234, 237, 0.2);
}

/* Focus ring for accessibility */
.custom-search-box__clear:focus-visible {
  outline: 2px solid #ccb6ff;
  outline-offset: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
  .custom-search-box {
    min-width: 100%;
    max-width: 100%;
  }

  .custom-search-box__container {
    border-radius: 40px;
  }

  .custom-search-box__input {
    font-size: 1rem;
    padding: 16px 8px 16px 0;
  }

  .custom-search-box__icon {
    padding: 0 18px;
  }
}

@media (max-width: 480px) {
  .custom-search-box__container {
    border-radius: 35px;
  }

  .custom-search-box__input {
    padding: 14px 8px 14px 0;
  }

  .custom-search-box__icon {
    padding: 0 16px;
  }
}

/* Dark theme enhancements */
.custom-search-box__container {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Animation for clear button */
.custom-search-box__clear {
  opacity: 0;
  transform: scale(0.8);
  animation: fadeInScale 0.2s ease forwards;
}

@keyframes fadeInScale {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hover effect for the entire container */
.custom-search-box__container:hover {
  /* transform: translateY(-1px); */
}

/* Active state */
.custom-search-box__container:active {
  transform: translateY(0);
}
