/* Enhanced Search Component - Material Design 3 2024 */

.enhanced-search {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.enhanced-search-field {
  position: relative;
  width: 100%;
}

.enhanced-search-input {
  width: 100%;
  --md-outlined-text-field-container-color: var(--md-sys-color-surface-container-high);
  --md-outlined-text-field-outline-color: var(--md-sys-color-outline-variant);
  --md-outlined-text-field-hover-outline-color: var(--md-sys-color-outline);
  --md-outlined-text-field-focus-outline-color: var(--md-sys-color-primary);
  --md-outlined-text-field-input-text-color: var(--md-sys-color-on-surface);
  --md-outlined-text-field-label-text-color: var(--md-sys-color-on-surface-variant);
  --md-outlined-text-field-supporting-text-color: var(--md-sys-color-on-surface-variant);
  --md-outlined-text-field-leading-icon-color: var(--md-sys-color-on-surface-variant);
  --md-outlined-text-field-trailing-icon-color: var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-large);
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized);
}

.enhanced-search-input:focus-within {
  --md-outlined-text-field-container-color: var(--md-sys-color-surface-container-highest);
  box-shadow: var(--md-sys-elevation-level1);
}

.enhanced-search-dropdown {
  position: absolute;
  top: calc(100% + var(--md-sys-spacing-2));
  left: 0;
  right: 0;
  z-index: 1000;
  animation: searchDropdownEnter var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized);
}

.enhanced-search-suggestions {
  --md-elevated-card-container-color: var(--md-sys-color-surface-container-high);
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level3);
  
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
  border-radius: var(--md-sys-shape-corner-large);
  backdrop-filter: blur(16px);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.enhanced-search-header {
  padding: var(--md-sys-spacing-4) var(--md-sys-spacing-4) var(--md-sys-spacing-2);
  color: var(--md-sys-color-on-surface-variant);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.enhanced-search-suggestion {
  --md-list-item-container-color: transparent;
  --md-list-item-hover-state-layer-color: var(--md-sys-color-on-surface);
  --md-list-item-hover-state-layer-opacity: 0.08;
  --md-list-item-focus-state-layer-color: var(--md-sys-color-on-surface);
  --md-list-item-focus-state-layer-opacity: 0.12;
  --md-list-item-pressed-state-layer-color: var(--md-sys-color-on-surface);
  --md-list-item-pressed-state-layer-opacity: 0.12;
  
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.enhanced-search-suggestion.focused {
  --md-list-item-container-color: var(--md-sys-color-secondary-container);
  --md-list-item-label-text-color: var(--md-sys-color-on-secondary-container);
  --md-list-item-supporting-text-color: var(--md-sys-color-on-secondary-container);
  --md-list-item-leading-icon-color: var(--md-sys-color-on-secondary-container);
}

.enhanced-search-suggestion:hover {
  --md-list-item-container-color: var(--md-sys-color-surface-container-highest);
}

.enhanced-search-no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-8);
  gap: var(--md-sys-spacing-2);
  color: var(--md-sys-color-on-surface-variant);
  text-align: center;
}

.enhanced-search-no-results md-icon {
  font-size: 48px;
  opacity: 0.6;
}

/* Animations */
@keyframes searchDropdownEnter {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 600px) {
  .enhanced-search {
    max-width: 100%;
  }
  
  .enhanced-search-suggestions {
    max-height: 300px;
  }
}

/* Dark Theme Adjustments */
@media (prefers-color-scheme: dark) {
  .enhanced-search-input {
    --md-outlined-text-field-container-color: var(--md-sys-color-surface-container);
  }
  
  .enhanced-search-input:focus-within {
    --md-outlined-text-field-container-color: var(--md-sys-color-surface-container-high);
  }
  
  .enhanced-search-suggestions {
    --md-elevated-card-container-color: var(--md-sys-color-surface-container);
    border-color: var(--md-sys-color-outline);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .enhanced-search-input {
    --md-outlined-text-field-outline-color: var(--md-sys-color-outline);
    border-width: 2px;
  }
  
  .enhanced-search-suggestions {
    border-width: 2px;
    border-color: var(--md-sys-color-outline);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .enhanced-search-input,
  .enhanced-search-suggestion {
    transition: none;
  }
  
  .enhanced-search-dropdown {
    animation: none;
  }
}
