/* Material Hero - Material Design 3 Official Style */

.material-hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  background: transparent;
}

.material-hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.material-hero-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.material-hero-shape.shape-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #6750a4, #7d5260);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.material-hero-shape.shape-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #7d5260, #625b71);
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.material-hero-shape.shape-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #625b71, #6750a4);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.material-hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: var(--md-sys-spacing-8);
}

.material-hero-icons {
  display: flex;
  justify-content: center;
  gap: var(--md-sys-spacing-4);
  margin-bottom: var(--md-sys-spacing-8);
}

.material-hero-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: iconPulse 3s ease-in-out infinite;
}

.material-hero-icon:nth-child(2) {
  animation-delay: 1s;
}

.material-hero-icon:nth-child(3) {
  animation-delay: 2s;
}

.material-hero-icon md-icon {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24px !important;
}

.material-hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 400;
  line-height: 1.1;
  margin: 0 0 var(--md-sys-spacing-6);
  color: rgba(255, 255, 255, 0.95);
  font-family: "Roboto", sans-serif;
  letter-spacing: -0.02em;
}

.material-hero-subtitle {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
  font-weight: 400;
  line-height: 1.5;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-family: "Roboto", sans-serif;
  max-width: 600px;
  margin: 0 auto;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .material-hero {
    min-height: 50vh;
  }
  
  .material-hero-content {
    padding: var(--md-sys-spacing-6);
  }
  
  .material-hero-icons {
    gap: var(--md-sys-spacing-3);
    margin-bottom: var(--md-sys-spacing-6);
  }
  
  .material-hero-icon {
    width: 40px;
    height: 40px;
  }
  
  .material-hero-icon md-icon {
    font-size: 20px !important;
  }
  
  .material-hero-shape.shape-1 {
    width: 120px;
    height: 120px;
  }
  
  .material-hero-shape.shape-2 {
    width: 100px;
    height: 100px;
  }
  
  .material-hero-shape.shape-3 {
    width: 80px;
    height: 80px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .material-hero-shape,
  .material-hero-icon {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .material-hero-title {
    color: white;
  }
  
  .material-hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .material-hero-icon {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
}
