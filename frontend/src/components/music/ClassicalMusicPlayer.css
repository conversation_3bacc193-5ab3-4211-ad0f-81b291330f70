/* Post-style Music Player */
.futuristic-player {
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
  font-family: "Roboto", "Inter", sans-serif;
  position: relative;
}

/* Hide default audio controls */
audio {
  display: none;
}

/* Main Player Container - Post Card Style */
.player-container {
  background: #444459;
  border-radius: 24px;
  padding: 0; /* 去除所有padding */
  position: relative;
  overflow: hidden;
  /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); */
  /* border: 1px solid rgba(255, 255, 255, 0.2); */
  height: 300px; /* 增加高度 */
  display: flex;
  flex-direction: column;
}

/* Album Art Section - 黄金分割上部分 */
.album-art-container {
  background: #cdb6ff;
  opacity: 0.6;
  position: relative;
  flex: 1.618; /* 黄金分割比例 */
  border-radius: 16px;
  /* padding: 16px 16px 8px 16px; 添加内部padding */
}

.album-art {
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px; /* 减小图标尺寸 */
  color: rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: hidden;
}

/* Audio Visualizer Bars - Only show when playing */
.audio-bars {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.audio-bars.playing {
  opacity: 1;
}

.audio-bar {
  width: 3px;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  animation: audioWave 1.5s ease-in-out infinite;
}

.audio-bar:nth-child(1) {
  height: 12px;
  animation-delay: 0s;
}
.audio-bar:nth-child(2) {
  height: 20px;
  animation-delay: 0.1s;
}
.audio-bar:nth-child(3) {
  height: 16px;
  animation-delay: 0.2s;
}
.audio-bar:nth-child(4) {
  height: 24px;
  animation-delay: 0.3s;
}
.audio-bar:nth-child(5) {
  height: 18px;
  animation-delay: 0.4s;
}
.audio-bar:nth-child(6) {
  height: 14px;
  animation-delay: 0.5s;
}
.audio-bar:nth-child(7) {
  height: 22px;
  animation-delay: 0.6s;
}
.audio-bar:nth-child(8) {
  height: 16px;
  animation-delay: 0.7s;
}
.audio-bar:nth-child(9) {
  height: 20px;
  animation-delay: 0.8s;
}
.audio-bar:nth-child(10) {
  height: 18px;
  animation-delay: 0.9s;
}
.audio-bar:nth-child(11) {
  height: 12px;
  animation-delay: 1s;
}
.audio-bar:nth-child(12) {
  height: 26px;
  animation-delay: 1.1s;
}

@keyframes audioWave {
  0%,
  100% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
}

/* Content Section - 黄金分割下部分 */
.content-section {
  flex: 1; /* 黄金分割比例 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* padding: 12px 16px 16px 16px; 添加内部padding */
}

/* Track Info */
.track-info {
  margin-top: 8px;
  text-align: center;
  margin-bottom: 8px; /* 减小间距 */
}

.track-title {
  font-size: 16px; /* 减小字体 */
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #e6e1e3;
  letter-spacing: -0.01em;
}

.track-artist {
  font-size: 12px; /* 减小字体 */
  color: #e6e1e3;
  margin: 0;
  font-weight: 400;
}

/* Controls Section */
.controls-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px; /* 减小间距 */
  margin-bottom: 16px;
}

.control-btn {
  background: #675595;
  border: none;
  border-radius: 50%;
  width: 48px; /* 统一尺寸 */
  height: 48px; /* 统一尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e6e1e3;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
  color: #e6e1e3;
  background: #7865a5;
  transform: scale(1.05);
}

.play-btn {
  background: #675595;
  border: none;
  border-radius: 50%;
  width: 48px; /* 统一尺寸 */
  height: 48px; /* 统一尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e6e1e3;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.play-btn:hover {
  background: #7865a5;
  transform: scale(1.05);
}

.play-btn:active {
  transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 480px) {
  .futuristic-player {
    max-width: 280px;
  }

  .player-container {
    height: 280px; /* 进一步压缩移动端高度 */
  }

  .album-art-container {
    /* padding: 12px 12px 6px 12px; 减小移动端padding */
  }

  .content-section {
    padding: 0 12px 12px 12px; /* 减小移动端padding */
  }

  .track-title {
    font-size: 14px;
  }

  .track-artist {
    font-size: 11px;
  }

  .play-btn {
    width: 40px;
    height: 40px;
  }

  .control-btn {
    width: 32px;
    height: 32px;
  }

  .controls-section {
    gap: 12px;
  }

  .album-art {
    font-size: 36px; /* 减小移动端图标 */
  }
}
