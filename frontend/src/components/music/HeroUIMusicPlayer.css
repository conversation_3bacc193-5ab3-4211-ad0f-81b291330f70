/* Apple-style Music Player Styles - True Glassmorphism */
.music-player-card {
  background: rgba(255, 255, 255, 0.25) !important;
  backdrop-filter: blur(20px) saturate(180%) brightness(110%);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.music-player-card:hover {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12), 0 4px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(255, 255, 255, 0.15);
}

/* Dark mode adjustments - More transparent for true glass effect */
.dark .music-player-card {
  background: rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(20px) saturate(180%) brightness(120%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 16px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
}

.dark .music-player-card:hover {
  background: rgba(0, 0, 0, 0.2) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(255, 255, 255, 0.08);
}

/* Apple Glass Effect - Enhanced for true glassmorphism */
.apple-glass-effect {
  position: relative;
  overflow: hidden;
}

.apple-glass-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  z-index: 1;
  pointer-events: none;
}

.apple-glass-effect::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 19px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.05) 30%,
    rgba(255, 255, 255, 0.02) 70%,
    rgba(255, 255, 255, 0.1) 100%
  );
  pointer-events: none;
  z-index: 0;
}

/* Dark mode glass effect adjustments */
.dark .apple-glass-effect::before {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%
  );
}

.dark .apple-glass-effect::after {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.02) 30%,
    rgba(255, 255, 255, 0.01) 70%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

/* Card Body Styles */
.music-player-card .heroui-card-body {
  padding: 24px;
  position: relative;
  z-index: 2;
}

/* Album Cover Styles with Apple-style effects */
.music-player-card .heroui-image {
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

/* Text Styles with Apple typography */
.music-player-card h3 {
  color: rgba(255, 255, 255, 0.95) !important;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 2px 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.music-player-card h1 {
  color: rgba(255, 255, 255, 0.98) !important;
  font-size: 20px;
  font-weight: 700;
  margin: 8px 0 0 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.music-player-card p {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  letter-spacing: -0.005em;
}

/* Dark mode text adjustments */
.dark .music-player-card h3 {
  color: rgba(255, 255, 255, 0.9) !important;
}

.dark .music-player-card h1 {
  color: rgba(255, 255, 255, 0.95) !important;
}

.dark .music-player-card p {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* Apple-style Control Button Styles */
.music-player-controls {
  gap: 20px;
  margin-top: 24px;
  padding: 0 8px;
}

.music-player-card .heroui-button {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  min-width: auto !important;
  padding: 12px !important;
  border-radius: 50% !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px) saturate(180%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.music-player-card .heroui-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.35) !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.music-player-card .heroui-button:active {
  transform: scale(0.98);
}

.music-player-card .heroui-button svg {
  width: 20px !important;
  height: 20px !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Main Play Button - True Apple Music glassmorphism style */
.music-player-card .play-button {
  background: rgba(255, 255, 255, 0.85) !important;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  padding: 16px !important;
  width: 64px !important;
  height: 64px !important;
  backdrop-filter: blur(10px) saturate(180%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.6),
    inset 0 -1px 0 rgba(255, 255, 255, 0.2);
}

.music-player-card .play-button:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 3px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(255, 255, 255, 0.3);
}

.music-player-card .play-button svg {
  width: 28px !important;
  height: 28px !important;
  color: rgba(0, 0, 0, 0.9) !important;
}

/* Dark mode button adjustments - Enhanced glassmorphism */
.dark .music-player-card .heroui-button {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(15px) saturate(180%) brightness(120%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.dark .music-player-card .heroui-button:hover {
  background: rgba(255, 255, 255, 0.18) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.dark .music-player-card .play-button {
  background: rgba(255, 255, 255, 0.85) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(10px) saturate(180%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    inset 0 -1px 0 rgba(255, 255, 255, 0.15);
}

.dark .music-player-card .play-button:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4), 0 3px 12px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.7),
    inset 0 -1px 0 rgba(255, 255, 255, 0.2);
}

/* Heart Button with Apple-style animation */
.music-player-card .heart-button svg {
  color: #ff4757 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.music-player-card .heart-button.liked svg {
  fill: #ff4757 !important;
  animation: appleHeartBeat 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes appleHeartBeat {
  0% {
    transform: scale(1);
  }
  15% {
    transform: scale(1.25);
  }
  30% {
    transform: scale(1.1);
  }
  45% {
    transform: scale(1.2);
  }
  60% {
    transform: scale(1.05);
  }
  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Apple-style Progress Bar */
.music-player-progress {
  margin: 20px 0;
}

.music-player-card .heroui-progress {
  background: rgba(255, 255, 255, 0.25) !important;
  height: 4px !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) saturate(180%);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.2);
}

.music-player-card .heroui-progress-indicator {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 255, 0.95) 100%
  ) !important;
  border-radius: 8px;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.4),
    0 1px 2px rgba(255, 255, 255, 0.6);
}

/* Dark mode progress bar - Enhanced glassmorphism */
.dark .music-player-card .heroui-progress {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .music-player-card .heroui-progress-indicator {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.95) 50%,
    rgba(255, 255, 255, 0.9) 100%
  ) !important;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.3),
    0 1px 2px rgba(255, 255, 255, 0.4);
}

/* Time Display with Apple typography */
.music-player-card .time-display {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: -0.005em;
  font-variant-numeric: tabular-nums;
}

.dark .music-player-card .time-display {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Focus states for accessibility */
.music-player-card .heroui-button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Control Section */
.music-player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 16px 0 8px 0;
}

/* Progress Section */
.music-player-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .music-player-card h3 {
    font-size: 20px;
  }

  .music-player-controls {
    gap: 4px;
  }

  .music-player-card .heroui-button svg {
    width: 18px !important;
    height: 18px !important;
  }
}

/* Fix for HeroUI component styling conflicts */
.music-player-card * {
  box-sizing: border-box;
}

.music-player-card .heroui-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure icons are visible */
.music-player-card svg {
  display: block !important;
  fill: currentColor;
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Force icon visibility for all button states */
.music-player-card button svg,
.music-player-card .heroui-button svg {
  display: block !important;
  width: 20px !important;
  height: 20px !important;
  color: white !important;
  fill: currentColor !important;
}

/* Special styling for play button icon */
.music-player-card .play-button svg {
  width: 24px !important;
  height: 24px !important;
}

/* Heart icon special styling */
.music-player-card .heart-button svg {
  color: #ff4757 !important;
}

.music-player-card .heart-button.liked svg {
  fill: #ff4757 !important;
}
