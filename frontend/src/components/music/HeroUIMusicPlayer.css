/* HeroUI Music Player Styles */
.music-player-card {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.music-player-card .heroui-card-body {
  padding: 16px;
}

/* Album Cover Styles */
.music-player-card .heroui-image {
  border-radius: 12px;
  overflow: hidden;
}

/* Text Styles */
.music-player-card h3 {
  color: white !important;
  font-size: 24px;
  font-weight: 700;
  margin: 8px 0 4px 0;
  line-height: 1.2;
}

.music-player-card p {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 14px;
  margin: 0;
}

/* But<PERSON> Styles */
.music-player-card .heroui-button {
  background: transparent !important;
  border: none !important;
  min-width: auto !important;
  padding: 8px !important;
  border-radius: 50% !important;
}

.music-player-card .heroui-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.music-player-card .heroui-button svg {
  width: 20px !important;
  height: 20px !important;
  color: white !important;
}

/* Play Button Special Styling */
.music-player-card .play-button {
  background: rgba(255, 255, 255, 0.2) !important;
  padding: 12px !important;
}

.music-player-card .play-button svg {
  width: 24px !important;
  height: 24px !important;
}

/* Heart Button */
.music-player-card .heart-button svg {
  color: #ff4757 !important;
}

.music-player-card .heart-button.liked svg {
  fill: #ff4757 !important;
}

/* Progress Bar */
.music-player-card .heroui-progress {
  background: rgba(255, 255, 255, 0.2) !important;
  height: 4px !important;
  border-radius: 2px !important;
}

.music-player-card .heroui-progress-indicator {
  background: white !important;
}

/* Time Display */
.music-player-card .time-display {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 12px;
  font-family: monospace;
}

/* Control Section */
.music-player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 16px 0 8px 0;
}

/* Progress Section */
.music-player-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .music-player-card h3 {
    font-size: 20px;
  }

  .music-player-controls {
    gap: 4px;
  }

  .music-player-card .heroui-button svg {
    width: 18px !important;
    height: 18px !important;
  }
}

/* Fix for HeroUI component styling conflicts */
.music-player-card * {
  box-sizing: border-box;
}

.music-player-card .heroui-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure icons are visible */
.music-player-card svg {
  display: block !important;
  fill: currentColor;
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Force icon visibility for all button states */
.music-player-card button svg,
.music-player-card .heroui-button svg {
  display: block !important;
  width: 20px !important;
  height: 20px !important;
  color: white !important;
  fill: currentColor !important;
}

/* Special styling for play button icon */
.music-player-card .play-button svg {
  width: 24px !important;
  height: 24px !important;
}

/* Heart icon special styling */
.music-player-card .heart-button svg {
  color: #ff4757 !important;
}

.music-player-card .heart-button.liked svg {
  fill: #ff4757 !important;
}
