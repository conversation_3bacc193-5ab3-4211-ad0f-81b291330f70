/* Music Upload Styles */

.music-upload {
  max-width: 800px;
  margin: 0 auto;
}

/* Error State */
.upload-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid var(--md-sys-color-error);
}

.upload-error md-icon {
  color: var(--md-sys-color-error);
}

/* Header */
.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  gap: 24px;
}

.upload-title h1 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.upload-title p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.upload-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.upload-actions md-filled-button,
.upload-actions md-text-button {
  --md-filled-button-container-shape: 12px;
  --md-text-button-container-shape: 12px;
  --md-filled-button-container-height: 48px;
  --md-text-button-container-height: 48px;
}

/* Upload Progress */
.upload-progress {
  background: var(--md-sys-color-surface-container);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-info span {
  color: var(--md-sys-color-on-surface);
}

/* Form */
.upload-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background: var(--md-sys-color-surface);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.form-section h2 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 24px 0;
  font-weight: 500;
}

/* File Upload */
.file-upload-section {
  margin-bottom: 24px;
}

.file-upload-section:last-child {
  margin-bottom: 0;
}

.file-upload-section label {
  display: block;
  color: var(--md-sys-color-on-surface);
  margin-bottom: 12px;
  font-weight: 500;
}

.file-drop-zone {
  border: 2px dashed var(--md-sys-color-outline-variant);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-drop-zone:hover {
  border-color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
}

.file-drop-zone.drag-over {
  border-color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
  transform: scale(1.02);
}

.file-drop-zone.has-file {
  border-color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
  border-style: solid;
}

.cover-zone {
  min-height: 80px;
  padding: 16px;
}

/* Drop Zone Content */
.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.6;
}

.drop-zone-content p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.drop-zone-content md-outlined-button {
  --md-outlined-button-container-shape: 12px;
}

/* File Info */
.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 16px;
  background: var(--md-sys-color-surface-container-high);
  border-radius: 12px;
}

.file-icon {
  font-size: 32px;
  color: var(--md-sys-color-primary);
}

.file-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.file-size {
  color: var(--md-sys-color-on-surface-variant);
}

/* Metadata Grid */
.metadata-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metadata-field {
  --md-outlined-text-field-container-shape: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .upload-actions {
    justify-content: flex-end;
  }
  
  .form-section {
    padding: 16px;
  }
  
  .file-drop-zone {
    padding: 24px 16px;
    min-height: 100px;
  }
  
  .upload-icon {
    font-size: 36px;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .file-info {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .upload-actions {
    flex-direction: column;
  }
  
  .upload-actions md-filled-button,
  .upload-actions md-text-button {
    width: 100%;
  }
  
  .file-drop-zone {
    padding: 16px;
    min-height: 80px;
  }
  
  .drop-zone-content {
    gap: 8px;
  }
  
  .upload-icon {
    font-size: 32px;
  }
  
  .file-info {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

/* Button States */
.upload-actions md-filled-button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-actions md-circular-progress {
  --md-circular-progress-size: 18px;
  --md-circular-progress-active-indicator-color: var(--md-sys-color-on-primary);
}

/* Focus States */
.metadata-field:focus-within {
  --md-outlined-text-field-outline-color: var(--md-sys-color-primary);
  --md-outlined-text-field-outline-width: 2px;
}

/* Linear Progress */
.upload-progress md-linear-progress {
  --md-linear-progress-active-indicator-color: var(--md-sys-color-primary);
  --md-linear-progress-track-color: var(--md-sys-color-surface-container-highest);
}
