/* Footer Styles - Google Material Design Style */

.footer {
  background: var(--md-sys-color-surface-container-low);
  border-top: 1px solid var(--md-sys-color-outline-variant);
  margin-top: auto;
  padding: var(--md-sys-spacing-12) 0 var(--md-sys-spacing-6);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--md-sys-spacing-6);
}

/* Main Footer Content */
.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--md-sys-spacing-12);
  margin-bottom: var(--md-sys-spacing-10);
}

/* Brand Section */
.footer-brand {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-3);
  margin-bottom: var(--md-sys-spacing-2);
}

.footer-logo-icon {
  font-family: "Material Symbols Outlined";
  font-size: 32px;
  color: var(--md-sys-color-primary);
  font-weight: 400;
}

.footer-logo-text {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  color: var(--md-sys-color-on-surface);
}

.footer-description {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  line-height: 1.5;
  max-width: 280px;
}

.footer-social {
  display: flex;
  gap: var(--md-sys-spacing-2);
  margin-top: var(--md-sys-spacing-2);
}

.footer-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--md-sys-shape-corner-full);
  background: transparent;
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.footer-social-link:hover {
  background: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-primary);
  border-color: var(--md-sys-color-primary);
}

.footer-social-link .material-symbols-outlined {
  font-size: 20px;
}

/* Links Sections */
.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--md-sys-spacing-8);
}

.footer-links-section {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

.footer-links-title {
  font-family: var(--md-sys-typescale-title-medium-font);
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: var(--md-sys-typescale-title-medium-weight);
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.footer-links-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-3);
}

.footer-link {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  transition: color 0.2s ease;
  padding: var(--md-sys-spacing-1) 0;
}

.footer-link:hover {
  color: var(--md-sys-color-primary);
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--md-sys-spacing-6);
  border-top: 1px solid var(--md-sys-color-outline-variant);
  gap: var(--md-sys-spacing-4);
}

.footer-bottom-left {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-3);
}

.footer-copyright {
  font-family: var(--md-sys-typescale-body-small-font);
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
}

.footer-legal-links {
  display: flex;
  gap: var(--md-sys-spacing-4);
  flex-wrap: wrap;
}

.footer-legal-link {
  font-family: var(--md-sys-typescale-body-small-font);
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-legal-link:hover {
  color: var(--md-sys-color-primary);
}

.footer-bottom-right {
  display: flex;
  align-items: center;
}

.footer-language-button {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  background: transparent;
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
  font-family: var(--md-sys-typescale-body-small-font);
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  transition: all 0.2s ease;
}

.footer-language-button:hover {
  background: var(--md-sys-color-surface-container);
  border-color: var(--md-sys-color-outline);
}

.footer-language-button .material-symbols-outlined {
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: var(--md-sys-spacing-8) 0 var(--md-sys-spacing-4);
  }

  .footer-container {
    padding: 0 var(--md-sys-spacing-4);
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-8);
    margin-bottom: var(--md-sys-spacing-8);
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--md-sys-spacing-6);
  }

  .footer-bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--md-sys-spacing-4);
  }

  .footer-bottom-left {
    width: 100%;
  }

  .footer-legal-links {
    gap: var(--md-sys-spacing-3);
  }

  .footer-bottom-right {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-6);
  }

  .footer-legal-links {
    flex-direction: column;
    gap: var(--md-sys-spacing-2);
  }

  .footer-social {
    justify-content: flex-start;
  }
}

/* Dark Theme Adjustments */
@media (prefers-color-scheme: dark) {
  .footer {
    background: var(--md-sys-color-surface-container-lowest);
  }
}
